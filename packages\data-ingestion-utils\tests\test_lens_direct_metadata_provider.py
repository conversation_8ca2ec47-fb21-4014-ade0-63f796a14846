import threading
import pytest
import tempfile
import os
import pickle
from unittest.mock import patch, MagicMock
from lens_direct_metadata_provider import LensDirectMetadataProvider


class LensDirectMetadataProviderTestHelper(LensDirectMetadataProvider):
    """
    Test helper class that extends LensDirectMetadataProvider without loading data from APIs.
    
    This class prevents the automatic loading of tables during initialization, allowing
    tests to manually set up asset data for controlled testing scenarios.
    """
    def __init__(self, scenario=None, max_retries=1):
        """
        Initialize the test helper without calling load_tables().
        
        Args:
            scenario (str): The scenario name (e.g., 'all_upstream_assets')
            max_retries (int): Maximum number of API retry attempts
        """
        self.scenario = scenario
        self.max_retries = max_retries
        self.assets_not_found = {
            'FIELD': set(),
            'PLAY_COMPANY_GEM': set(),
            'PLANT': set(),
            'TRANSPORT': set(),
        }
        self.asset_dict = {
            'FIELD': {},
            'PLAY_COMPANY_GEM': {},
            'PLANT': {},
            'TRANSPORT': {},
        }
        self._not_found_lock = threading.Lock()
        # Do NOT call self.load_tables() to avoid API calls during testing


def test_field_metadata():
    """
    Test field metadata retrieval functionality.
    
    This test verifies that:
    1. Field metadata can be retrieved by asset ID
    2. All expected field columns are returned with correct values
    3. Missing field assets return empty dict and are tracked in not_found set
    4. The FIELD_COLUMN_MAPPING is used correctly for field data structure
    """
    provider = LensDirectMetadataProviderTestHelper(scenario='all_upstream_assets', max_retries=3)
    provider.asset_dict['FIELD'] = {
        1: {k: f"field_{k}_1" for k in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys()},
        2: {k: f"field_{k}_2" for k in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys()},
    }
    meta = provider.get_asset_metadata(asset_id=1, asset_type='FIELD')
    assert isinstance(meta, dict)
    for k in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert meta[k] == f"field_{k}_1"
    missing = provider.get_asset_metadata(asset_id=999, asset_type='FIELD')
    assert missing == {}
    assert 999 in provider.assets_not_found['FIELD']


def test_plant_metadata():
    """
    Test plant (LNG plant) metadata retrieval functionality.
    
    This test verifies that:
    1. Plant metadata can be retrieved by asset ID using 'PLANT' asset type
    2. All expected plant columns are returned with correct values
    3. Missing plant assets return empty dict and are tracked in not_found set
    4. The PLANT_COLUMN_MAPPING is used correctly for plant data structure
    """
    provider = LensDirectMetadataProviderTestHelper(scenario='all_upstream_assets')
    provider.asset_dict['PLANT'] = {
        10: {k: f"plant_{k}_10" for k in LensDirectMetadataProvider.PLANT_COLUMN_MAPPING.keys()},
    }
    meta = provider.get_asset_metadata(asset_id=10, asset_type='PLANT')
    assert isinstance(meta, dict)
    for k in LensDirectMetadataProvider.PLANT_COLUMN_MAPPING.keys():
        assert meta[k] == f"plant_{k}_10"
    missing = provider.get_asset_metadata(asset_id=999, asset_type='PLANT')
    assert missing == {}
    assert 999 in provider.assets_not_found['PLANT']


def test_pipeline_metadata():
    """
    Test pipeline (transport) metadata retrieval functionality.
    
    This test verifies that:
    1. Pipeline metadata can be retrieved by asset ID using 'TRANSPORT' asset type
    2. All expected pipeline columns are returned with correct values
    3. Missing pipeline assets return empty dict and are tracked in not_found set
    4. The PIPELINE_COLUMN_MAPPING is used correctly for pipeline data structure
    """
    provider = LensDirectMetadataProviderTestHelper(scenario='all_upstream_assets')
    provider.asset_dict['TRANSPORT'] = {
        20: {k: f"pipe_{k}_20" for k in LensDirectMetadataProvider.PIPELINE_COLUMN_MAPPING.keys()},
    }
    meta = provider.get_asset_metadata(asset_id=20, asset_type='TRANSPORT')
    assert isinstance(meta, dict)
    for k in LensDirectMetadataProvider.PIPELINE_COLUMN_MAPPING.keys():
        assert meta[k] == f"pipe_{k}_20"
    missing = provider.get_asset_metadata(asset_id=999, asset_type='TRANSPORT')
    assert missing == {}
    assert 999 in provider.assets_not_found['TRANSPORT']


def test_scenario_not_all_upstream_assets():
    """
    Test scenario filtering functionality.
    
    This test verifies that when the scenario is NOT 'all_upstream_assets',
    the metadata provider returns empty dictionaries instead of actual metadata.
    This ensures that metadata is only provided for the correct scenario context,
    preventing data leakage between different analysis scenarios.
    """
    provider = LensDirectMetadataProviderTestHelper(scenario='all_upstream_companies')
    provider.asset_dict['FIELD'] = {1: {k: "val" for k in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys()}}
    meta = provider.get_asset_metadata(asset_id=1, asset_type='FIELD')
    assert meta == {}


def test_asset_not_found_tracking():
    """
    Test that missing assets are properly tracked across all asset types.
    
    This test verifies that:
    1. When assets are not found in the asset_dict, they return empty metadata
    2. Missing asset IDs are correctly added to the assets_not_found tracking sets
    3. Each asset type (FIELD, PLANT, TRANSPORT) maintains separate tracking
    4. The tracking mechanism works consistently across different asset types
    """
    provider = LensDirectMetadataProviderTestHelper(scenario='all_upstream_assets')
    provider.asset_dict['FIELD'] = {}
    provider.asset_dict['PLANT'] = {}
    provider.asset_dict['TRANSPORT'] = {}
    provider.get_asset_metadata(asset_id=123, asset_type='FIELD')
    provider.get_asset_metadata(asset_id=456, asset_type='PLANT')
    provider.get_asset_metadata(asset_id=789, asset_type='TRANSPORT')
    assert 123 in provider.assets_not_found['FIELD']
    assert 456 in provider.assets_not_found['PLANT']
    assert 789 in provider.assets_not_found['TRANSPORT']


def test_print_count_assets_not_found(capsys):
    """
    Test the reporting functionality for missing assets.
    
    This test verifies that:
    1. The print_count_assets_not_found method correctly outputs counts for each asset type
    2. Output format includes asset type names and correct counts
    3. Zero counts are properly displayed (important for completeness)
    4. All asset types are included in the output (FIELD, PLANT, TRANSPORT, PLAY_COMPANY_GEM)
    
    Uses capsys fixture to capture and verify printed output.
    """
    provider = LensDirectMetadataProviderTestHelper(scenario='all_upstream_assets')
    provider.assets_not_found = {
        'FIELD': {1, 2},
        'PLANT': {3},
        'TRANSPORT': set(),
        'PLAY_COMPANY_GEM': {4, 5, 6},
    }
    provider.print_count_assets_not_found()
    captured = capsys.readouterr()
    assert "Assets not found for FIELD: 2" in captured.out
    assert "Assets not found for PLANT: 1" in captured.out
    assert "Assets not found for TRANSPORT: 0" in captured.out
    assert "Assets not found for PLAY_COMPANY_GEM: 3" in captured.out


def test_add_asset_duplicate_detection(capsys):
    """
    Test duplicate asset detection and logging.
    
    This test verifies that:
    1. The _add_asset method detects duplicate asset IDs within the same asset type
    2. Duplicate detection works correctly for all asset types (FIELD, PLAY_COMPANY_GEM, PLANT, TRANSPORT)
    3. Appropriate warning message is printed for duplicates (no ValueError is raised)
    4. The warning messages include both the asset type and the duplicate ID for debugging
    5. The asset is still added/updated (overwrites the previous value)
    6. Processing continues gracefully without exceptions when duplicates are encountered
    
    This is useful to detect data issues on Lens Direct (data provider side) while still
    allowing the data processing to continue without failing.
    """
    provider = LensDirectMetadataProviderTestHelper(scenario='all_upstream_assets')
    
    # FIELD
    provider._add_asset('FIELD', 1, {'foo': 'bar'})
    provider._add_asset('FIELD', 1, {'foo': 'baz'})  # This should print a warning
    captured = capsys.readouterr()
    assert "Duplicate asset id detected for type FIELD: 1" in captured.out
    assert provider.asset_dict['FIELD'][1] == {'foo': 'baz'}  # Should be overwritten

    # PLAY_COMPANY_GEM
    provider._add_asset('PLAY_COMPANY_GEM', 2, {'foo': 'bar'})
    provider._add_asset('PLAY_COMPANY_GEM', 2, {'foo': 'baz'})  # This should print a warning
    captured = capsys.readouterr()
    assert "Duplicate asset id detected for type PLAY_COMPANY_GEM: 2" in captured.out

    # PLANT
    provider._add_asset('PLANT', 3, {'foo': 'bar'})
    provider._add_asset('PLANT', 3, {'foo': 'baz'})  # This should print a warning
    captured = capsys.readouterr()
    assert "Duplicate asset id detected for type PLANT: 3" in captured.out

    # TRANSPORT
    provider._add_asset('TRANSPORT', 4, {'foo': 'bar'})
    provider._add_asset('TRANSPORT', 4, {'foo': 'baz'})  # This should print a warning
    captured = capsys.readouterr()
    assert "Duplicate asset id detected for type TRANSPORT: 4" in captured.out


def test_duplicate_asset_no_exception_raised():
    """
    Test that duplicate asset IDs do not raise ValueError exceptions.
    
    This test explicitly verifies that the recent change to not raise ValueError
    for duplicate IDs is working correctly. Instead of raising exceptions,
    the system should gracefully handle duplicates by logging warnings and 
    overwriting the existing record.
    """
    provider = LensDirectMetadataProviderTestHelper(scenario='all_upstream_assets')
    
    # Test that no exception is raised when adding duplicate FIELD assets
    try:
        provider._add_asset('FIELD', 100, {'name': 'test_field_1'})
        provider._add_asset('FIELD', 100, {'name': 'test_field_2'})  # Duplicate ID
        # If we reach this point, no exception was raised - which is expected behavior
        assert provider.asset_dict['FIELD'][100]['name'] == 'test_field_2'
    except ValueError as e:
        pytest.fail(f"ValueError was raised for duplicate FIELD asset: {e}")
    
    # Test that no exception is raised when adding duplicate PLANT assets
    try:
        provider._add_asset('PLANT', 200, {'name': 'test_plant_1'})
        provider._add_asset('PLANT', 200, {'name': 'test_plant_2'})  # Duplicate ID
        # If we reach this point, no exception was raised - which is expected behavior
        assert provider.asset_dict['PLANT'][200]['name'] == 'test_plant_2'
    except ValueError as e:
        pytest.fail(f"ValueError was raised for duplicate PLANT asset: {e}")
    
    # Test that no exception is raised when adding duplicate TRANSPORT assets
    try:
        provider._add_asset('TRANSPORT', 300, {'name': 'test_transport_1'})
        provider._add_asset('TRANSPORT', 300, {'name': 'test_transport_2'})  # Duplicate ID
        # If we reach this point, no exception was raised - which is expected behavior
        assert provider.asset_dict['TRANSPORT'][300]['name'] == 'test_transport_2'
    except ValueError as e:
        pytest.fail(f"ValueError was raised for duplicate TRANSPORT asset: {e}")
    
    # Test that no exception is raised when adding duplicate PLAY_COMPANY_GEM assets
    try:
        provider._add_asset('PLAY_COMPANY_GEM', 400, {'name': 'test_gem_1'})
        provider._add_asset('PLAY_COMPANY_GEM', 400, {'name': 'test_gem_2'})  # Duplicate ID
        # If we reach this point, no exception was raised - which is expected behavior
        assert provider.asset_dict['PLAY_COMPANY_GEM'][400]['name'] == 'test_gem_2'
    except ValueError as e:
        pytest.fail(f"ValueError was raised for duplicate PLAY_COMPANY_GEM asset: {e}")


def test_build_company_name_filter_cases():
    provider = LensDirectMetadataProviderTestHelper()
    method = provider._build_company_name_filter

    cases = [
        # (input, expected_output)
        (["Alpha"], "company_name eq 'Alpha'"),
        (["Alpha & Beta"], "(startswith(company_name,'Alpha') and endswith(company_name,'Beta'))"),
        (["Alpha & Beta & Gamma"], "(startswith(company_name,'Alpha') and contains(company_name,'Beta') and endswith(company_name,'Gamma'))"),
        (["Alpha && Beta"], "(startswith(company_name,'Alpha') and endswith(company_name,'Beta'))"),
        (["&&Beta"], "endswith(company_name,'Beta')"),
        (["Beta&&"], "startswith(company_name,'Beta')"),
        (["&&Beta&&"], "contains(company_name,'Beta')"),
        (["&Beta&"], "contains(company_name,'Beta')"),
        (["&&&"], ""),
        (["   "], "company_name eq '   '"),
        ([""], "company_name eq ''"),
        (["A&B&C&D"], "(startswith(company_name,'A') and contains(company_name,'B') and contains(company_name,'C') and endswith(company_name,'D'))"),
        (["A&&B&&C"], "(startswith(company_name,'A') and contains(company_name,'B') and endswith(company_name,'C'))"),
        (["&A&B&"], "(contains(company_name,'A') and contains(company_name,'B'))"),
        (["A&B"], "(startswith(company_name,'A') and endswith(company_name,'B'))"),
        (["A&&B"], "(startswith(company_name,'A') and endswith(company_name,'B'))"),
        (["A&B&&"], "(startswith(company_name,'A') and contains(company_name,'B'))"),
        (["&&A&B"], "(contains(company_name,'A') and endswith(company_name,'B'))"),
        (["A&B&C"], "(startswith(company_name,'A') and contains(company_name,'B') and endswith(company_name,'C'))"),
        (["A"], "company_name eq 'A'"),
        (["&A"], "endswith(company_name,'A')"),
        (["A&"], "startswith(company_name,'A')"),
    ]

    for input_names, expected in cases:
        result = method(input_names)
        # Remove outer parentheses for single filter for easier comparison
        if result.startswith('(') and result.endswith(')') and result.count('(') == 1:
            result = result[1:-1]
        assert result == expected, f"Failed for input {input_names!r}: got {result!r}, expected {expected!r}"

    # Test multiple companies (should join with ' or ')
    result = method(["Alpha", "Beta & Gamma"])
    assert result == "company_name eq 'Alpha' or (startswith(company_name,'Beta') and endswith(company_name,'Gamma'))"

    # Test all empty
    result = method(["", "&&&"])
    assert result == "company_name eq '' or "


# Disk-based Memory Optimization Tests

def test_disk_storage_initialization():
    """
    Test that disk storage is properly initialized for company scenarios.
    """
    with patch('lens_direct_metadata_provider.get_static_list_upstream_companies') as mock_companies:
        mock_companies.return_value = {"TestCompany": {"id": "123", "asset_count": 10}}

        # Test with disk storage enabled (default)
        with patch.object(LensDirectMetadataProvider, '_load_tables'):
            provider = LensDirectMetadataProvider(scenario='all_upstream_companies', use_disk_storage=True)

            assert provider.use_disk_storage is True
            assert provider.temp_dir is not None
            assert os.path.exists(provider.temp_dir)
            assert provider.company_data_files == {}

            # Cleanup
            provider.cleanup_disk_storage()

        # Test with disk storage disabled
        with patch.object(LensDirectMetadataProvider, '_load_tables'):
            provider_no_disk = LensDirectMetadataProvider(scenario='all_upstream_companies', use_disk_storage=False)

            assert provider_no_disk.use_disk_storage is False
            assert provider_no_disk.temp_dir is None


@patch('lens_direct_metadata_provider.fetch_field_company_history_table')
@patch('lens_direct_metadata_provider.fetch_pipeline_company_history_table')
@patch('lens_direct_metadata_provider.fetch_plant_company_history_table')
def test_disk_storage_write_and_read(mock_plant, mock_pipeline, mock_field):
    """
    Test that company data is correctly written to disk and read back.
    """
    # Mock API responses
    mock_field.return_value = [
        {
            'id_valuation': 123,
            'company_name': 'TestCompany',
            'id_field_gem': 'FIELD_123',
            'field_operator': 'TestOperator'
        }
    ]
    mock_pipeline.return_value = []
    mock_plant.return_value = []

    with patch('lens_direct_metadata_provider.get_static_list_upstream_companies') as mock_companies:
        mock_companies.return_value = {"TestCompany": {"id": "123", "asset_count": 10}}

        provider = LensDirectMetadataProvider(scenario='all_upstream_companies', use_disk_storage=True)

        # Verify data was written to disk
        assert len(provider.company_data_files) > 0
        assert 'TestCompany' in provider.company_data_files

        # Verify the file exists
        file_path = provider.company_data_files['TestCompany']
        assert os.path.exists(file_path)

        # Test reading data back
        company_data = provider._load_company_data_from_disk('TestCompany')
        assert company_data is not None
        assert 'FIELD' in company_data
        assert 123 in company_data['FIELD']
        assert 'TestCompany' in company_data['FIELD'][123]

        # Test get_company_asset_metadata with disk storage
        result = provider.get_company_asset_metadata(123, 'FIELD', 'TestCompany')
        assert result['operator'] == 'TestOperator'

        # Cleanup
        provider.cleanup_disk_storage()


def test_memory_usage_comparison():
    """
    Test that disk storage uses less memory than in-memory storage.
    This is a conceptual test - in practice, you'd use memory profiling tools.
    """
    with patch('lens_direct_metadata_provider.get_static_list_upstream_companies') as mock_companies:
        mock_companies.return_value = {"TestCompany": {"id": "123", "asset_count": 10}}

        # Test memory mode - company_asset_dict should contain data
        with patch.object(LensDirectMetadataProvider, '_load_tables'):
            provider_memory = LensDirectMetadataProvider(scenario='all_upstream_companies', use_disk_storage=False)

            # Simulate adding data to memory
            provider_memory.company_asset_dict['FIELD'][123] = {'TestCompany': {'operator': 'TestOperator'}}

            # Verify data is in memory
            assert len(provider_memory.company_asset_dict['FIELD']) > 0

        # Test disk mode - company_asset_dict should be empty after writing to disk
        with patch.object(LensDirectMetadataProvider, '_load_tables'):
            provider_disk = LensDirectMetadataProvider(scenario='all_upstream_companies', use_disk_storage=True)

            # Simulate the disk writing process
            provider_disk.company_asset_dict['FIELD'][123] = {'TestCompany': {'operator': 'TestOperator'}}
            provider_disk._write_company_data_to_disk()
            provider_disk._clear_company_memory_data()

            # Verify memory is cleared
            assert len(provider_disk.company_asset_dict['FIELD']) == 0

            # But data should be available via disk
            assert len(provider_disk.company_data_files) > 0

            # Cleanup
            provider_disk.cleanup_disk_storage()


def test_disk_storage_cleanup():
    """
    Test that temporary files are properly cleaned up.
    """
    with patch('lens_direct_metadata_provider.get_static_list_upstream_companies') as mock_companies:
        mock_companies.return_value = {"TestCompany": {"id": "123", "asset_count": 10}}

        with patch.object(LensDirectMetadataProvider, '_load_tables'):
            provider = LensDirectMetadataProvider(scenario='all_upstream_companies', use_disk_storage=True)

            temp_dir = provider.temp_dir
            assert os.path.exists(temp_dir)

            # Create a test file
            test_file = os.path.join(temp_dir, 'test.pkl')
            with open(test_file, 'wb') as f:
                pickle.dump({'test': 'data'}, f)

            assert os.path.exists(test_file)

            # Test cleanup
            provider.cleanup_disk_storage()

            # Directory should be removed
            assert not os.path.exists(temp_dir)
            assert provider.temp_dir is None
            assert provider.company_data_files == {}


def test_print_total_loaded_metadata_disk_storage():
    """
    Test that print_total_loaded_metadata shows appropriate information for disk storage.
    """
    with patch('lens_direct_metadata_provider.get_static_list_upstream_companies') as mock_companies:
        mock_companies.return_value = {"Company1": {"id": "1", "asset_count": 10}, "Company2": {"id": "2", "asset_count": 5}}

        with patch.object(LensDirectMetadataProvider, '_load_tables'):
            provider = LensDirectMetadataProvider(scenario='all_upstream_companies', use_disk_storage=True)

            # Simulate some company data files
            provider.company_data_files = {"Company1": "/tmp/company1.pkl"}

            # Capture print output
            import io
            import sys
            captured_output = io.StringIO()
            sys.stdout = captured_output

            provider.print_total_loaded_metadata()

            sys.stdout = sys.__stdout__
            output = captured_output.getvalue()

            assert "Company-asset metadata using disk storage" in output
            assert "Total companies available: 2" in output
            assert "Companies with data files: 1" in output
            assert "stored on disk to minimize memory usage" in output

            # Cleanup
            provider.cleanup_disk_storage()


def test_disk_storage_error_handling():
    """
    Test error handling in disk storage operations.
    """
    with patch('lens_direct_metadata_provider.get_static_list_upstream_companies') as mock_companies:
        mock_companies.return_value = {"TestCompany": {"id": "123", "asset_count": 10}}

        with patch.object(LensDirectMetadataProvider, '_load_tables'):
            provider = LensDirectMetadataProvider(scenario='all_upstream_companies', use_disk_storage=True)

            # Test loading non-existent company data
            result = provider._load_company_data_from_disk('NonExistentCompany')
            assert result is None

            # Test get_company_asset_metadata with non-existent company
            result = provider.get_company_asset_metadata(123, 'FIELD', 'NonExistentCompany')
            assert result == {}

            # Cleanup
            provider.cleanup_disk_storage()
