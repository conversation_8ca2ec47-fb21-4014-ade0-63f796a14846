#!/usr/bin/env python3
"""
Demonstration script showing the disk-based memory optimization for LensDirectMetadataProvider.

This script shows how the new disk storage feature reduces memory usage by storing
company data on disk instead of keeping it all in memory.
"""

import tempfile
import os
import pickle
import sys
from typing import Dict, Any


class DiskStorageDemo:
    """
    Simplified demonstration of the disk storage approach used in LensDirectMetadataProvider.
    """
    
    def __init__(self, use_disk_storage: bool = True):
        self.use_disk_storage = use_disk_storage
        self.company_asset_dict = {
            'FIELD': {},
            'PLAY_COMPANY_GEM': {},
            'PLANT': {},
            'TRANSPORT': {},
        }
        
        if self.use_disk_storage:
            self.temp_dir = tempfile.mkdtemp(prefix='lens_direct_demo_')
            self.company_data_files = {}
            print(f"Using disk storage in: {self.temp_dir}")
        else:
            self.temp_dir = None
            self.company_data_files = {}
            print("Using memory storage")
    
    def simulate_bulk_data_loading(self, num_companies: int = 100, assets_per_company: int = 50):
        """
        Simulate loading bulk company data (the memory-intensive operation we're optimizing).
        """
        print(f"Simulating bulk loading of {num_companies} companies with {assets_per_company} assets each...")
        
        # Simulate the original memory-intensive approach
        for company_idx in range(num_companies):
            company_name = f"Company_{company_idx}"
            
            for asset_idx in range(assets_per_company):
                asset_id = company_idx * 1000 + asset_idx
                record = {
                    'operator': f'Operator_{company_idx}',
                    'region': f'Region_{company_idx % 10}',
                    'asset_type': 'FIELD',
                    'production_rate': asset_idx * 100
                }
                
                # Add to memory (this is what causes the memory issue)
                if asset_id not in self.company_asset_dict['FIELD']:
                    self.company_asset_dict['FIELD'][asset_id] = {}
                self.company_asset_dict['FIELD'][asset_id][company_name] = record
        
        print(f"Loaded {num_companies * assets_per_company} records into memory")
        
        # If using disk storage, write to disk and clear memory
        if self.use_disk_storage:
            self._write_company_data_to_disk()
            self._clear_memory_data()
    
    def _write_company_data_to_disk(self):
        """Write company data to disk files."""
        print("Writing company data to disk...")
        
        # Group data by company
        company_data = {}
        for asset_id, companies in self.company_asset_dict['FIELD'].items():
            for company_name, record in companies.items():
                if company_name not in company_data:
                    company_data[company_name] = {'FIELD': {}}
                if asset_id not in company_data[company_name]['FIELD']:
                    company_data[company_name]['FIELD'][asset_id] = {}
                company_data[company_name]['FIELD'][asset_id][company_name] = record
        
        # Write each company to a separate file
        for company_name, data in company_data.items():
            file_path = os.path.join(self.temp_dir, f"{company_name}.pkl")
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
            self.company_data_files[company_name] = file_path
        
        print(f"Wrote {len(company_data)} company files to disk")
    
    def _clear_memory_data(self):
        """Clear data from memory after writing to disk."""
        print("Clearing memory data...")
        for asset_type in self.company_asset_dict:
            self.company_asset_dict[asset_type].clear()
        print("Memory cleared")
    
    def get_company_asset_metadata(self, asset_id: int, company_name: str):
        """
        Get company asset metadata - demonstrates the disk vs memory access.
        """
        if self.use_disk_storage:
            # Load from disk
            if company_name in self.company_data_files:
                file_path = self.company_data_files[company_name]
                with open(file_path, 'rb') as f:
                    company_data = pickle.load(f)
                
                if asset_id in company_data['FIELD'] and company_name in company_data['FIELD'][asset_id]:
                    return company_data['FIELD'][asset_id][company_name]
            return {}
        else:
            # Load from memory
            if asset_id in self.company_asset_dict['FIELD'] and company_name in self.company_asset_dict['FIELD'][asset_id]:
                return self.company_asset_dict['FIELD'][asset_id][company_name]
            return {}
    
    def get_memory_usage_info(self):
        """Get information about current memory usage."""
        if self.use_disk_storage:
            memory_records = sum(
                sum(len(companies) for companies in assets.values())
                for assets in self.company_asset_dict.values()
            )
            disk_files = len(self.company_data_files)
            return {
                'mode': 'disk',
                'memory_records': memory_records,
                'disk_files': disk_files,
                'temp_dir': self.temp_dir
            }
        else:
            memory_records = sum(
                sum(len(companies) for companies in assets.values())
                for assets in self.company_asset_dict.values()
            )
            return {
                'mode': 'memory',
                'memory_records': memory_records,
                'disk_files': 0,
                'temp_dir': None
            }
    
    def cleanup(self):
        """Clean up temporary files."""
        if self.temp_dir and os.path.exists(self.temp_dir):
            import shutil
            shutil.rmtree(self.temp_dir)
            print(f"Cleaned up: {self.temp_dir}")


def main():
    """Demonstrate the memory optimization."""
    print("=== Lens Direct Metadata Provider - Disk Storage Demo ===\n")
    
    # Test 1: Memory-based approach (original)
    print("1. Testing MEMORY-based approach (original):")
    demo_memory = DiskStorageDemo(use_disk_storage=False)
    demo_memory.simulate_bulk_data_loading(num_companies=50, assets_per_company=20)
    
    memory_info = demo_memory.get_memory_usage_info()
    print(f"Memory usage: {memory_info['memory_records']} records in memory")
    
    # Test accessing data
    result = demo_memory.get_company_asset_metadata(1005, "Company_1")
    print(f"Sample data access: {result}")
    print()
    
    # Test 2: Disk-based approach (optimized)
    print("2. Testing DISK-based approach (optimized):")
    demo_disk = DiskStorageDemo(use_disk_storage=True)
    demo_disk.simulate_bulk_data_loading(num_companies=50, assets_per_company=20)
    
    disk_info = demo_disk.get_memory_usage_info()
    print(f"Memory usage: {disk_info['memory_records']} records in memory")
    print(f"Disk usage: {disk_info['disk_files']} files on disk")
    
    # Test accessing data
    result = demo_disk.get_company_asset_metadata(1005, "Company_1")
    print(f"Sample data access: {result}")
    print()
    
    # Comparison
    print("3. Comparison:")
    print(f"Memory approach: {memory_info['memory_records']} records in memory")
    print(f"Disk approach: {disk_info['memory_records']} records in memory, {disk_info['disk_files']} files on disk")
    
    memory_reduction = memory_info['memory_records'] - disk_info['memory_records']
    print(f"Memory reduction: {memory_reduction} records ({memory_reduction/memory_info['memory_records']*100:.1f}%)")
    
    # Cleanup
    demo_disk.cleanup()
    
    print("\n=== Demo Complete ===")
    print("The disk-based approach successfully reduces memory usage by storing")
    print("company data on disk instead of keeping it all in memory.")


if __name__ == "__main__":
    main()
